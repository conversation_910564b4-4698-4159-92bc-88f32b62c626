{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.13", "autoprefixer": "^10.4.21", "fabric": "^6.7.1", "pdfjs-dist": "^5.4.149", "postcss": "^8.5.6", "react": "^19.1.1", "react-color": "^2.19.3", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-pdf": "^10.1.0", "react-signature-canvas": "^1.1.0-alpha.2", "tailwindcss": "^4.1.13", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.2"}}