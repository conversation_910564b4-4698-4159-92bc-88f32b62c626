# PDF处理器使用指南

## 🚀 快速开始

### 1. 启动应用
```bash
# 安装依赖
npm run install:all

# 启动开发服务器
npm run dev
```

访问 http://localhost:5173 查看前端界面
后端API运行在 http://localhost:3002

### 2. 上传PDF文件
- 拖拽PDF文件到上传区域
- 或点击选择文件按钮
- 支持最大50MB的PDF文件

### 3. 使用编辑功能
1. 上传PDF后，点击"编辑"标签
2. 从左侧工具面板选择编辑工具：
   - **选择** - 选择和移动对象
   - **文本** - 添加文本注释
   - **矩形** - 绘制矩形
   - **圆形** - 绘制圆形
   - **箭头** - 绘制箭头
   - **高亮** - 高亮文本
   - **签名** - 添加签名
   - **图片** - 插入图片

3. 在右侧编辑画布上进行操作
4. 使用右侧属性面板调整对象属性

## 📋 功能说明

### PDF基础操作
- **合并PDF** - 将多个PDF文件合并为一个
- **分割PDF** - 按页数范围分割PDF
- **转换为图片** - 将PDF页面转换为PNG图片

### 编辑功能
- **文本编辑** - 双击文本对象进行编辑
- **对象操作** - 拖拽移动、调整大小、旋转
- **属性调整** - 修改颜色、字体、透明度等
- **图层管理** - 置于顶层/底层操作

### 快捷键
- `Delete` / `Backspace` - 删除选中对象
- 拖拽 - 移动对象
- 双击文本 - 编辑文本内容

## 🔧 API接口

### 上传文件
```
POST /api/pdf/upload
Content-Type: multipart/form-data
Body: { pdf: File }
```

### 合并PDF
```
POST /api/pdf/merge
Content-Type: multipart/form-data
Body: { pdfs: File[] }
```

### 分割PDF
```
POST /api/pdf/split
Content-Type: multipart/form-data
Body: { pdf: File, startPage: number, endPage: number }
```

### 转换为图片
```
POST /api/pdf/to-images
Content-Type: multipart/form-data
Body: { pdf: File }
```

### 下载文件
```
GET /api/pdf/download/:filename
```

## 🐛 故障排除

### 常见问题

**1. 文件上传失败**
- 检查文件大小是否超过50MB
- 确认文件格式为PDF
- 检查网络连接

**2. PDF无法显示**
- 确认PDF文件没有损坏
- 检查浏览器是否支持PDF.js
- 尝试刷新页面

**3. 编辑功能不工作**
- 确认已选择正确的工具
- 检查浏览器控制台是否有错误
- 尝试重新加载页面

**4. 样式显示异常**
- 清除浏览器缓存
- 检查CSS文件是否正确加载
- 确认使用现代浏览器

### 开发调试

**启动开发模式：**
```bash
# 前端开发服务器
cd client && npm run dev

# 后端开发服务器
cd server && npm run dev
```

**查看日志：**
- 前端：浏览器开发者工具控制台
- 后端：终端输出

**测试API：**
```bash
# 健康检查
curl http://localhost:3002/api/health

# 测试文件上传
curl -X POST -F "pdf=@test.pdf" http://localhost:3002/api/pdf/upload
```

## 📚 技术细节

### 前端架构
- **React** - 用户界面框架
- **Zustand** - 状态管理
- **Fabric.js** - 画布编辑
- **React PDF** - PDF渲染
- **React Dropzone** - 文件上传

### 后端架构
- **Express** - Web服务器
- **PDF-lib** - PDF处理
- **Multer** - 文件上传
- **Sharp** - 图片处理

### 文件结构
```
pdf-processor/
├── client/          # React前端
├── server/          # Node.js后端
├── README.md        # 项目说明
├── USAGE.md         # 使用指南
└── package.json     # 项目配置
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License - 详见LICENSE文件
